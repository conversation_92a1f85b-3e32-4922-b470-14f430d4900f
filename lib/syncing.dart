import 'dart:convert';
import 'dart:io';

import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/storage/data_manager.dart';
import 'package:storetrack_app/core/utils/task_utils.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/entities/submit_report_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/sync_pic_request_entity.dart'
    as sync_pic;
import 'package:storetrack_app/features/home/<USER>/entities/sync_sign_request_entity.dart'
    as sync_sign;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/upload_photo_request_entity.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    hide FollowupTask;
import 'package:storetrack_app/features/home/<USER>/entities/upload_sign_request_entity.dart';

Future<String> encodeFileToBase64(String filePath) async {
  final file = File(filePath);
  final bytes = await file.readAsBytes();
  final base64String = base64Encode(bytes);
  return base64String;
}

void schedule(){
  var task = TaskDetail();
  task.taskStatus = "Confirmed";
  task.scheduledTimeStamp = // selectedDate;
  task.submissionTimeStamp = DateTime.now();
 // task.
}

Future<void> submitReportData() async {
  const String actualDeviceUid = "8b7a6774c878a206";
  const String actualAppVersion = "9.9.9";
  var dataManager = sl<DataManager>();
  // get from realm db
  // final realm = sl<RealmDatabase>().realm;
  final tasks = TaskUtils.getTasksWithSyncPending();

  List<TaskDetail> taskDetailModelsToBeUploaded =
      tasks.map((e) => TaskDetailMapper.toEntity(e)).toList();
  for (var taskDetailModel in taskDetailModelsToBeUploaded) {
    SubmitReportRequestEntity request = SubmitReportRequestEntity();
    request.token = await dataManager.getAuthToken();
    request.userId = await dataManager.getUserId();
    request.deviceUid = actualDeviceUid;
    request.appversion = actualAppVersion;
    request.taskId = taskDetailModel.taskId.toString();
    request.comment = taskDetailModel.comment;
    request.minutes = taskDetailModel.minutes?.toInt();
    request.timerMinutes = 0;
    request.claimableKms = taskDetailModel.claimableKms?.toInt();
    request.pages = taskDetailModel.pages?.toInt();
    request.taskStatus = taskDetailModel.taskStatus;
    request.submissionState = -1;
    request.taskCommencementTimeStamp =
        taskDetailModel.taskCommencementTimeStamp;
    request.taskStoppedTimeStamp = taskDetailModel.taskStoppedTimeStamp;
    request.scheduledTimeStamp = taskDetailModel.scheduledTimeStamp;
    request.submissionTimeStamp = DateTime.now();
    request.startTaskLatitude = taskDetailModel.taskLatitude?.toInt();
    request.startTaskLongitude = taskDetailModel.taskLongitude?.toInt();
    request.taskLatitude = taskDetailModel.latitude?.toInt();
    request.taskLongitude = taskDetailModel.longitude?.toInt();
    for (var form in taskDetailModel.forms!) {
      var formPost = Form();
      formPost.formId = form.formId;
      for (var questionAnswer in form.questionAnswers!) {
        if ((questionAnswer.isComment == false) ||
            (questionAnswer.isComment == true &&
                questionAnswer.measurementTextResult != null)) {
          var questionAnswerPost = QuestionAnswer(
            questionId: questionAnswer.questionId,
            questionpartId: questionAnswer.questionpartId,
            questionPartMultiId: questionAnswer.questionPartMultiId,
            measurementId: questionAnswer.measurementId,
            measurementTypeId: questionAnswer.measurementTypeId,
            measurementOptionId: questionAnswer.measurementOptionId,
            measurementOptionIds: questionAnswer.measurementOptionIds,
            measurementTextResult: questionAnswer.measurementTextResult,
            isComment: questionAnswer.isComment,
            commentTypeId: questionAnswer.commentTypeId,
          );
          formPost.questionAnswers?.add(questionAnswerPost);
          request.forms?.add(formPost);
        }
      }
    }
    // followup tasks
    for (var followupTask in taskDetailModel.followupTasks!) {
      var followupTaskPost = FollowupTask();
      followupTaskPost.taskId = taskDetailModel.taskId.toString();
      followupTaskPost.visitDate = followupTask.selectedVisitDate;
      followupTaskPost.followupTypeId = 0;
      followupTaskPost.followupItemId = 0;
      followupTaskPost.budget = followupTask.selectedBudget?.toInt();
      followupTaskPost.scheduleNote = followupTask.selectedScheduleNote;
      followupTaskPost.followupNumber = followupTask.followupNumber?.toInt();
      request.followupTasks?.add(followupTaskPost);
    }
    // resume pause items
    // no need to send resume pause items
    request.budgetCalculated = 0;
  }
}

Future<void> uploadSignatureData() async {
  final realm = sl<RealmDatabase>().realm;
  final tasks = realm.all<TaskDetailModel>();
  final tasksPost = tasks.map((e) => TaskDetailMapper.toEntity(e)).toList();
  var dataManager = sl<DataManager>();

  for (var task in tasksPost) {
    for (var signatureFolder in task.signatureFolder!) {
      for (var signature in signatureFolder.signatures!) {
        var signaturePost = UploadSignRequestEntity();
        signaturePost.token = await dataManager.getAuthToken();
        signaturePost.userId = int.parse(await dataManager.getUserId() ?? "0");
        signaturePost.taskId = task.taskId?.toInt();
        signaturePost.folderId = signatureFolder.folderId?.toInt();
        signaturePost.signatureId = signature.signatureId?.toInt();
        signaturePost.signedBy = signature.signedBy;
        signaturePost.cannotUploadMandatory = signature.cannotUploadMandatory;
        signaturePost.formId = signature.formId?.toInt();
        signaturePost.questionId = signature.questionId?.toInt();
        if (signaturePost.cannotUploadMandatory == true) {
          signaturePost.signatureBlob = "-1";
        } else {
          signaturePost.signatureBlob =
              await encodeFileToBase64(signature.signatureUrl ?? "");
        }
      }
    }
  }
}

Future<void> uploadPhotoData() async {
  final realm = sl<RealmDatabase>().realm;
  final tasks = realm.all<TaskDetailModel>();
  final tasksPost = tasks.map((e) => TaskDetailMapper.toEntity(e)).toList();
  var dataManager = sl<DataManager>();

  for (var task in tasksPost) {
    for (var photoFolder in task.photoFolder!) {
      for (var photo in photoFolder.photos!) {
        var photoPost = UploadPhotoRequestEntity();
        photoPost.token = await dataManager.getAuthToken();
        photoPost.userId = int.parse(await dataManager.getUserId() ?? "0");
        photoPost.taskId = task.taskId?.toInt();
        photoPost.folderId = photoFolder.folderId?.toInt();
        photoPost.photoId = photo.photoId?.toInt();
        photoPost.photoDate = null;
        photoPost.photoCaption = photo.caption;
        photoPost.cannotUploadMandatory = photo.cannotUploadMandatory;
        photoPost.formId = photo.formId?.toInt();
        photoPost.questionId = photo.questionId?.toInt();
        photoPost.measurementId = photo.measurementId?.toInt();
        photoPost.questionpartId = photo.questionpartId?.toInt();
        photoPost.questionPartMultiId = photo.questionPartMultiId;
        photoPost.measurementPhototypeId =
            photo.measurementPhototypeId?.toInt();
        if (photoPost.cannotUploadMandatory == true) {
          photoPost.pictureBlob = "-1";
        } else {
          photoPost.pictureBlob =
              await encodeFileToBase64(photo.photoUrl ?? "");
        }
      }
    }
  }
}

Future<void> deletePhotoData() async {
  final realm = sl<RealmDatabase>().realm;
  final tasks = realm.all<TaskDetailModel>();
  final tasksPost = tasks.map((e) => TaskDetailMapper.toEntity(e)).toList();
  final dataManager = sl<DataManager>();
  const deviceUid = "8b7a6774c878a206";

  var syncPicRequest = sync_pic.SyncPicInfoRequestEntity();
  List<sync_pic.Task> tasksSync = [];

  for (var task in tasksPost) {
    List<sync_pic.PhotoFolder> photoFolderList = [];
    for (var photoFolder in task.photoFolder!) {
      var deletePhotosIds = [];
      for (var photo in photoFolder.photos!) {
        if (photo.userDeletedPhoto == true) {
          deletePhotosIds.add(photo.photoId?.toInt());
        }
      }
      if (deletePhotosIds.isNotEmpty) {
        photoFolderList.add(sync_pic.PhotoFolder(
          folderId: photoFolder.folderId?.toInt(),
          deletePhotosIds: deletePhotosIds.join(","),
        ));
      }
    }
    if (photoFolderList.isNotEmpty) {
      tasksSync.add(sync_pic.Task(
        taskId: task.taskId?.toInt(),
        uploadPhotosSuccess: true,
        modifiedTimeStampPhotos: task.modifiedTimeStampPhotos,
        photoFolder: photoFolderList,
      ));
    }
  }
  syncPicRequest.tasks = tasksSync;
  syncPicRequest.token = await dataManager.getAuthToken();
  syncPicRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
  syncPicRequest.deviceuid = deviceUid;
}

Future<void> deleteSignatureData() async {
  final realm = sl<RealmDatabase>().realm;
  final tasks = realm.all<TaskDetailModel>();
  final tasksPost = tasks.map((e) => TaskDetailMapper.toEntity(e)).toList();
  var dataManager = sl<DataManager>();
  var syncSignRequest = sync_sign.SyncSignInfoRequestEntity();
  const deviceUid = "8b7a6774c878a206";

  List<sync_sign.Task> tasksSync = [];
  for (var task in tasksPost) {
    List<sync_sign.SignatureFolder> signatureList = [];
    for (var signatureFolder in task.signatureFolder!) {
      var deleteSignaturesIds = [];
      for (var signature in signatureFolder.signatures!) {
        if (signature.userDeletedSignature == true) {
          deleteSignaturesIds.add(signature.signatureId?.toInt());
        }
      }
      if (deleteSignaturesIds.isNotEmpty) {
        signatureList.add(sync_sign.SignatureFolder(
          folderId: signatureFolder.folderId?.toInt(),
          deleteSignaturesIds: deleteSignaturesIds.join(","),
        ));
      }
    }
    if (signatureList.isNotEmpty) {
      tasksSync.add(sync_sign.Task(
        taskId: task.taskId?.toInt(),
        signatureFolder: signatureList,
        uploadSignatureSuccess: true,
        modifiedTimeStampSignatures: task.modifiedTimeStampSignatures,
      ));
    }
  }
  syncSignRequest.tasks = tasksSync;
  syncSignRequest.token = await dataManager.getAuthToken();
  syncSignRequest.userId = int.parse(await dataManager.getUserId() ?? "0");
  syncSignRequest.deviceuid = deviceUid;
}

Future<void> downloadTaskDataSimplified() async {
  final realm = sl<RealmDatabase>().realm;
  final tasks = realm.all<TaskDetailModel>();
  final tasksPost = tasks.map((e) => TaskDetailMapper.toEntity(e)).toList();
  final dataManager = sl<DataManager>();
  final userId = await dataManager.getUserId() ?? "";
  final token = await dataManager.getAuthToken() ?? "";
  const deviceUid = "8b7a6774c878a206";
  const appversion = "9.9.9";

  TasksRequestEntity tasksRequest = TasksRequestEntity(
    deviceUid: deviceUid,
    userId: userId,
    appversion: appversion,
    tasks: const [],
    token: token,
  );

  List<Task> taskList = [];
  for (var task in tasksPost) {
    var taskItem = Task();
    taskItem.taskId = task.taskId?.toString();
    taskItem.scheduledTimeStamp = task.scheduledTimeStamp;
    taskItem.submissionTimeStamp = task.submissionTimeStamp;
    taskItem.modifiedTimeStampTask = task.modifiedTimeStampTask;
    taskItem.modifiedTimeStampPhotos = task.modifiedTimeStampPhotos;
    taskItem.modifiedTimeStampSignatures = task.modifiedTimeStampSignatures;
    taskItem.modifiedTimeStampSignaturetypes =
        task.modifiedTimeStampSignaturetypes;
    taskItem.modifiedTimeStampPhototypes = task.modifiedTimeStampPhototypes;
    taskItem.modifiedTimeStampForms = task.modifiedTimeStampForms;
    taskItem.modifiedTimeStampDocuments = task.modifiedTimeStampDocuments;
    taskItem.phototypeIds = [];
    taskItem.forceImportFollowupTask = false;
    taskList.add(taskItem);
  }
  tasksRequest.tasks.addAll(taskList);
}

processTasksResponse() {
  var tasksResponse = TasksResponseEntity();
  // var tasks = tasksResponse.data;
}

class Helper {
  final int taskId;
  final List<Taskmember> taskMembers;
  Helper({required this.taskId, required this.taskMembers});

  factory Helper.fromJson(Map<String, dynamic> json) {
    return Helper(
      taskId: json['task_id'],
      taskMembers: json['task_members'] != null
          ? List<Taskmember>.from(
              json['task_members']!.map((x) => Taskmember.fromJson(x)))
          : [],
    );
  }
}

//  @SuppressLint("StaticFieldLeak")
//     private void uploadSignatureData() {
//         final int[] numberOfUploads = {0};

//         if ((hasNetworkConnection()) && (CommonFunction.isActiveUserExist(mContext))) {
//             new AsyncTask<Void, Void, Void>() {
//                 @SuppressLint("StaticFieldLeak")
//                 @Override
//                 protected Void doInBackground(final Void... params) {

//                     for (final TaskDetailModel taskDetailModel : DatabaseManager.getInstance(mContext).getAllTaskDetailModelsInBackground(false)) {

//                         for (final SignatureFolderModel signatureFolderModel : taskDetailModel.getTaskSignatureFolders()) {

//                             for (final SignatureModel signatureModel : signatureFolderModel.getSignatureFolderContents()) {
//                                 if ((((signatureModel.isSignatureSignedUpdated()) || (signatureModel.isSignatureItemUpdated())) && (!signatureModel.isSignatureIsDeleted()))) {

//                                     if ((taskSignatureUploadStatus != null) && (taskDetailModel != null) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskID()))) {
//                                         taskSignatureUploadStatus.put(taskDetailModel.getTaskID(), true);
//                                         CommonFunction.print("Uploaded signatures count: STATUS A1" + taskDetailModel.getTaskID() + " : " + " TRUE");

//                                     }

//                                     String requestURL = API.PRODUCTION_URL + "send_task_sig_v4_11";

//                                     LinkedHashMap<String, Object> requestBodyMap = new LinkedHashMap<>();
//                                     requestBodyMap.put("token", StorageManager.getFCMdeviceTokenFromSharedPreference(mContext));
//                                     requestBodyMap.put("user_id", StorageManager.getActiveUserIDFromSharedPreference(mContext));
//                                     requestBodyMap.put("task_id", taskDetailModel.getTaskID());
//                                     requestBodyMap.put("folder_id", signatureFolderModel.getSignatureFolderID());
//                                     requestBodyMap.put("signature_id", signatureModel.getSignatureId());
//                                     requestBodyMap.put("signed_by", (signatureModel.getSignedBy() != null ? signatureModel.getSignedBy() : ""));
//                                     requestBodyMap.put("cannot_upload_mandatory", signatureModel.isCannotUploadMandatory());

//                                     //mpt extra information
//                                     requestBodyMap.put("form_id", signatureFolderModel.getSignatureFolderID());
//                                     requestBodyMap.put("question_id", signatureModel.getQuestionID());

// //
//                                     if (signatureModel.isCannotUploadMandatory()) {
//                                         requestBodyMap.put("signature_blob", "-1");
//                                     } else if ((signatureModel.isSignatureItemUpdated()) && (!CommonFunction.isEmptyStringField(signatureModel.getSignatureLocalPath()))) {
//                                         //convert image to string 64.
//                                         String signatureBlobString = CommonFunction.encodeFileToBase64String(signatureModel.getSignatureLocalPath());

//                                         if (CommonFunction.isEmptyStringField(signatureBlobString)) {
//                                             signatureBlobString = CommonFunction.encodeBitmapToBase64String(((BitmapDrawable) ContextCompat.getDrawable(mContext, R.drawable.no_signature)).getBitmap());
//                                             requestBodyMap.put("user_deleted_signature", true);
//                                         }

//                                         requestBodyMap.put("signature_blob", signatureBlobString);
//                                     }

//                                     HeaderJsonObjectRequest requestObject = new HeaderJsonObjectRequest(Request.Method.POST, requestURL, new JSONObject(requestBodyMap),
//                                             new Response.Listener<JSONObject>() {
//                                                 @Override
//                                                 public void onResponse(JSONObject responseObject) {
//                                                     CommonFunction.print("Uploaded signatures count: Network response: SIG B " + responseObject.toString());

//                                                     numberOfUploads[0]--;

//                                                     if (CommonFunction.isActiveUserExist(mContext)) {
//                                                         JSONObject dataObject = responseObject.optJSONObject("data");
//                                                         if (dataObject != null) {
//                                                             //mo, currently API returns existing photoID for the new photo if user (or ipad mini) uploads same photo twice in a measurement.
//                                                             boolean duplicateSignatureID = DatabaseManager.getInstance(mContext).checkServerSignatureIDExistAlready(mRealm, taskDetailModel.getTaskID(), signatureFolderModel.getSignatureFolderID(), dataObject);

//                                                             CommonFunction.print("Uploaded signatures count: Network response: SIG B " + duplicateSignatureID);

//                                                             if (duplicateSignatureID && Integer.valueOf(signatureModel.getSignatureId()) < 0) {
//                                                                 //mo created, newly added photo was sent to the server but server returned existing photo id >> user uploaded duplicate pictures >> delete this photo model
//                                                                 //this is quick resolution. app should not allow importing duplicate photos.
//                                                                 CommonFunction.print("Uploaded signatures count: Network response: SIG DELETE " + signatureModel.getSignatureId());

//                                                                 DatabaseManager.getInstance(mContext).deleteSignature(mRealm, taskDetailModel.getTaskID(), signatureFolderModel.getSignatureFolderID(), signatureModel.getSignatureId());

//                                                             } else {
//                                                                 //send_task_pic returned valid response after Insert() or Update()
//                                                                 //>> update local photo model with server info. (local photo file will be deleted below to get photo from server)
//                                                                 CommonFunction.print("Uploaded signatures count: Network response: SIG UPDATE " + signatureModel.getSignatureId() + " : " + signatureModel.isCannotUploadMandatory());

//                                                                 DatabaseManager.getInstance(mContext).updateLocalSignatureModelAfterSendTaskPic(mRealm, taskDetailModel.getTaskID(), signatureFolderModel.getSignatureFolderID(), signatureModel.getQuestionID(), dataObject);
//                                                             }
//                                                         }
//                                                     }

//                                                     if (numberOfUploads[0] == 0) {
//                                                         //mo, all photos are uploaded. >> call api/sync_pic_info
//                                                         deleteSignatureData();
//                                                     }
//                                                 }
//                                             },
//                                             new Response.ErrorListener() {
//                                                 @Override
//                                                 public void onErrorResponse(VolleyError error) {
//                                                     numberOfUploads[0]--;

//                                                     if ((taskSignatureUploadStatus != null) && (taskDetailModel != null) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskID()))) {
//                                                         taskSignatureUploadStatus.put(taskDetailModel.getTaskID(), false);
//                                                         CommonFunction.print("Uploaded signatures count: STATUS A " + taskDetailModel.getTaskID() + " : " + " FALSE");
//                                                     }

//                                                     if (numberOfUploads[0] == 0) {
//                                                         deleteSignatureData();
//                                                     }
//                                                 }
//                                             });

//                                     requestObject.setTag(API_UPLOAD_TASK_SIGNATURE_REQUEST_TAG);

//                                     if ((hasNetworkConnection()) && (CommonFunction.isActiveUserExist(mContext))) {
//                                         numberOfUploads[0]++;

//                                         int socketTimeout = 30000; // 30 seconds
//                                         RetryPolicy policy = new DefaultRetryPolicy(socketTimeout, 0, DefaultRetryPolicy.DEFAULT_BACKOFF_MULT);
//                                         requestObject.setRetryPolicy(policy);

//                                         addToRequestQueue(requestObject);
//                                     } else {
//                                         numberOfUploads[0]--;

//                                         if ((taskSignatureUploadStatus != null) && (taskDetailModel != null) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskID()))) {
//                                             taskSignatureUploadStatus.put(taskDetailModel.getTaskID(), false);
//                                             CommonFunction.print("Uploaded signatures count: STATUS B " + taskDetailModel.getTaskID() + " : " + " FALSE");

//                                         }
//                                     }
//                                 }
//                             }
//                         }
//                     }

//                     return null;
//                 }

//                 @Override
//                 protected void onPostExecute(final Void result) {
//                     Date endExecutionTime = Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime();
//                     CommonFunction.print("Uploaded signatures count: " + numberOfUploads[0]);
//                     //CommonFunction.print("Upload photo process time: " + (endExecutionTime.getTime() - startExecutionTime.getTime()));

//                     if (numberOfUploads[0] == 0) {
//                         deleteSignatureData();
//                     }
//                 }
//             }.execute();
//         } else {
//             deleteSignatureData();
//         }
//     }

//     @SuppressLint("StaticFieldLeak")
//     private void uploadPhotoData() {
//         final int[] numberOfUploads = {0};

//         if ((hasNetworkConnection()) && (CommonFunction.isActiveUserExist(mContext))) {

//             new AsyncTask<Void, Void, Void>() {
//                 @Override
//                 protected Void doInBackground(final Void... params) {
//                     DateFormat dateFormat = new SimpleDateFormat(Constant.SERVER_TASK_DATEFORMAT_STRING, Constant.DEFAULT_LOCALE_INSTANCE);
//                     dateFormat.setTimeZone(Constant.DEFAULT_TIMEZONE_INSTANCE);

//                     for (final TaskDetailModel taskDetailModel : DatabaseManager.getInstance(mContext).getAllTaskDetailModelsInBackground(false)) {

//                         for (final PhotoFolderModel photoFolderModel : taskDetailModel.getTaskPhotoFolders()) {

//                             //mo, why photoFolderModel.getPhotoFolderContents() size is 0 at below?
//                             //debug
// //                            if (taskDetailModel.getTaskID().equals("6947248")) {
// //                                CommonFunction.print("photos size: " + photoFolderModel.getPhotoFolderContents().size());
// //                            }

//                             for (final PhotoModel photoModel : photoFolderModel.getPhotoFolderContents()) {
//                                 if (((photoModel.isPhotoCaptionUpdated()) || (photoModel.isPhotoItemUpdated())) && (!photoModel.isPhotoIsDeleted())) {

//                                     if (taskPhotoUploadStatus != null && !CommonFunction.isEmptyStringField(taskDetailModel.getTaskID())) {
//                                         taskPhotoUploadStatus.put(taskDetailModel.getTaskID(), true);
//                                     }

//                                     String requestURL = API.PRODUCTION_URL + "send_task_pic_v4_11";

//                                     LinkedHashMap<String, Object> requestBodyMap = new LinkedHashMap<>();
//                                     requestBodyMap.put("token", StorageManager.getFCMdeviceTokenFromSharedPreference(mContext));
//                                     requestBodyMap.put("user_id", StorageManager.getActiveUserIDFromSharedPreference(mContext));
//                                     requestBodyMap.put("task_id", taskDetailModel.getTaskID());
//                                     requestBodyMap.put("folder_id", photoFolderModel.getPhotoFolderID());
//                                     requestBodyMap.put("photo_id", photoModel.getPhotoID());
//                                     requestBodyMap.put("photo_date", photoModel.getPhotoDate());
//                                     requestBodyMap.put("photo_caption", (photoModel.getPhotoCaption() != null ? photoModel.getPhotoCaption() : ""));
//                                     requestBodyMap.put("cannot_upload_mandatory", photoModel.isCannotUploadMandatory());

//                                     //mo, 16/5/18
//                                     requestBodyMap.put("questionpart_id", photoModel.getQuestionPartID());
//                                     requestBodyMap.put("question_part_multi_id", photoModel.getQuestionMultiPartID());
//                                     requestBodyMap.put("measurement_phototype_id", photoModel.getMeasurementPhotoTypeID());

//                                     //mpt extra information
//                                     requestBodyMap.put("form_id", photoModel.getFormID());
//                                     requestBodyMap.put("question_id", photoModel.getQuestionID());
//                                     requestBodyMap.put("measurement_id", photoModel.getMeasurementID());

//                                     if (photoModel.isCannotUploadMandatory()) {
//                                         requestBodyMap.put("picture_blob", "-1");
//                                     } else if ((photoModel.isPhotoItemUpdated()) && (!CommonFunction.isEmptyStringField(photoModel.getPhotoLocalPath()))) {
//                                         //convert image to string 64.
//                                         String pictureBlobString = CommonFunction.encodeFileToBase64String(photoModel.getPhotoLocalPath());

//                                         if (CommonFunction.isEmptyStringField(pictureBlobString)) {
//                                             pictureBlobString = CommonFunction.encodeBitmapToBase64String(((BitmapDrawable) ContextCompat.getDrawable(mContext, R.drawable.img_nophoto)).getBitmap());
//                                             requestBodyMap.put("user_deleted_photo", true);
//                                         }

//                                         requestBodyMap.put("picture_blob", pictureBlobString);
//                                     }

//                                     CommonFunction.printLongString("Uploaded photos count: Network Request: " + new JSONObject(requestBodyMap).toString());

//                                     //requestURL == /api/send_)task_pic?
//                                     String jsonString = requestBodyMap.toString();

//                                     HeaderJsonObjectRequest requestObject = new HeaderJsonObjectRequest(Request.Method.POST, requestURL, new JSONObject(requestBodyMap),
//                                             new Response.Listener<JSONObject>() {
//                                                 @SuppressLint("StaticFieldLeak")
//                                                 @Override
//                                                 public void onResponse(JSONObject responseObject) {

//                                                     CommonFunction.print("Uploaded photos count: Network response: " + responseObject.toString());

//                                                     numberOfUploads[0]--;

//                                                     if (CommonFunction.isActiveUserExist(mContext)) {
//                                                         JSONObject dataObject = responseObject.optJSONObject("data");
//                                                         if (dataObject != null) {
//                                                             //mo, currently API returns existing photoID for the new photo if user (or ipad mini) uploads same photo twice in a measurement.
//                                                             boolean duplicatePhotoID = DatabaseManager.getInstance(mContext).checkServerPhotoIDExistAlready(mRealm, taskDetailModel.getTaskID(), photoFolderModel.getPhotoFolderID(), dataObject);

//                                                           //  CommonFunction.print("Uploaded photos count: Network response: DUP " + duplicatePhotoID + " : " + taskDetailModel.getTaskID() + " : " + photoFolderModel.getPhotoFolderID() + " : ");

//                                                             if (duplicatePhotoID && Integer.valueOf(photoModel.getPhotoID()) < 0) {
//                                                                 //mo created, newly added photo was sent to the server but server returned existing photo id >> user uploaded duplicate pictures >> delete this photo model
//                                                                 //this is quick resolution. app should not allow importing duplicate photos.

//                                                              //   CommonFunction.print("Uploaded photos count: Network response: DUP DEL " + duplicatePhotoID + " : " + taskDetailModel.getTaskID() + " : " + photoFolderModel.getPhotoFolderID() + " : " + photoModel.getPhotoID());

//                                                                 //HERE I AM DELETING PHOTOS
//                                                                 DatabaseManager.getInstance(mContext).deletePhoto(
//                                                                         mRealm, taskDetailModel.getTaskID(), photoFolderModel.getPhotoFolderID(), photoModel.getPhotoID()
//                                                                 );
//                                                             } else {
//                                                                 //send_task_pic returned valid response after Insert() or Update()
//                                                                 //>> update local photo model with server info. (local photo file will be deleted below to get photo from server)

//                                                                // CommonFunction.print("Uploaded photos count: Network response: DUP UPDTAE " + duplicatePhotoID + " : " + taskDetailModel.getTaskID() + " : " + photoFolderModel.getPhotoFolderID() + " : " + photoModel.getPhotoID());

//                                                                 DatabaseManager.getInstance(mContext).updateLocalPhotoModelAfterSendTaskPic(mRealm, taskDetailModel.getTaskID(), photoFolderModel.getPhotoFolderID(), photoModel.getPhotoID(), dataObject);
//                                                             }
//                                                         }
//                                                     }

//                                                     if (numberOfUploads[0] == 0) {
//                                                         //mo, all photos are uploaded. >> call api/sync_pic_info
//                                                         deletePhotoData();
//                                                     }
//                                                 }
//                                             },
//                                             new Response.ErrorListener() {
//                                                 @Override
//                                                 public void onErrorResponse(VolleyError error) {
//                                                     numberOfUploads[0]--;

//                                                     if ((taskPhotoUploadStatus != null) && (taskDetailModel != null) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskID()))) {
//                                                         taskPhotoUploadStatus.put(taskDetailModel.getTaskID(), false);
//                                                     }

//                                                     if (numberOfUploads[0] == 0) {
//                                                         deletePhotoData();
//                                                     }
//                                                 }
//                                             });

//                                     requestObject.setTag(API_UPLOAD_TASK_PHOTO_REQUEST_TAG);

//                                     if ((hasNetworkConnection()) && (CommonFunction.isActiveUserExist(mContext))) {
//                                         numberOfUploads[0]++;

//                                         int socketTimeout = 30000; // 30 seconds
//                                         RetryPolicy policy = new DefaultRetryPolicy(socketTimeout, 0, DefaultRetryPolicy.DEFAULT_BACKOFF_MULT);
//                                         requestObject.setRetryPolicy(policy);

//                                         addToRequestQueue(requestObject);
//                                     } else {
//                                         numberOfUploads[0]--;

//                                         if ((taskPhotoUploadStatus != null) && (taskDetailModel != null) && (!CommonFunction.isEmptyStringField(taskDetailModel.getTaskID()))) {
//                                             taskPhotoUploadStatus.put(taskDetailModel.getTaskID(), false);
//                                         }
//                                     }
//                                 }else{
//                                     System.out.println("not running");
//                                 }
//                             }
//                         }
//                     }

//                     return null;
//                 }

//                 @Override
//                 protected void onPostExecute(final Void result) {
//                     Date endExecutionTime = Calendar.getInstance(Constant.DEFAULT_TIMEZONE_INSTANCE, Constant.DEFAULT_LOCALE_INSTANCE).getTime();
//                     CommonFunction.print("Uploaded photos count: " + numberOfUploads[0]);
//                     //CommonFunction.print("Upload photo process time: " + (endExecutionTime.getTime() - startExecutionTime.getTime()));

//                     if (numberOfUploads[0] == 0) {
//                         deletePhotoData();
//                     }
//                 }
//             }.execute();
//         } else {
//             deletePhotoData();
//         }
//     }

//  //mo, Misleading function name. This func is for "sync_pic_info".
//     @SuppressLint("StaticFieldLeak")
//     private void deletePhotoData() {
//         if ((hasNetworkConnection()) && (CommonFunction.isActiveUserExist(mContext))) {
//             new AsyncTask<Void, Void, Void>() {
//                 @Override
//                 protected Void doInBackground(final Void... params) {
//                     DateFormat dateFormat = new SimpleDateFormat(Constant.SERVER_TASK_DATEFORMAT_STRING, Constant.DEFAULT_LOCALE_INSTANCE);
//                     dateFormat.setTimeZone(Constant.DEFAULT_TIMEZONE_INSTANCE);

//                     int deletedPhotoCount = 0;

//                   //  CommonFunction.print("calling /api/sync_pic_info_mpt...");

//                     String requestURL = API.PRODUCTION_URL + "sync_pic_info_mpt";

//                     HashMap<String, Object> requestBodyMap = new HashMap<>();
//                     requestBodyMap.put("token", StorageManager.getFCMdeviceTokenFromSharedPreference(mContext));
//                     requestBodyMap.put("user_id", StorageManager.getActiveUserIDFromSharedPreference(mContext));

//                     ArrayList<Object> taskList = new ArrayList<>();

//                     for (TaskDetailModel taskDetailModel : DatabaseManager.getInstance(mContext).getAllTaskDetailModelsInBackground(false)) {
//                         ArrayList<Object> photoFolderList = new ArrayList<>();

//                         for (PhotoFolderModel photoFolderModel : taskDetailModel.getTaskPhotoFolders()) {
//                             ArrayList<String> deletedPhotoIDs = new ArrayList<>();

//                             for (PhotoModel photoModel : photoFolderModel.getPhotoFolderContents()) {

//                                 if (photoModel.isPhotoIsDeleted()) {

//                                     CommonFunction.printLongString("Uploaded photos count: DEL IMG " + photoModel.getPhotoID() + " : " + photoModel.isPhotoIsDeleted());
//                                     deletedPhotoCount++;
//                                     deletedPhotoIDs.add(photoModel.getPhotoID());
//                                 }
//                             }

//                             if (!deletedPhotoIDs.isEmpty()) {
//                                 HashMap<String, Object> photoFolderDict = new HashMap<>();
//                                 photoFolderDict.put("folder_id", photoFolderModel.getPhotoFolderID());
//                                 photoFolderDict.put("delete_photos_ids", CommonFunction.combineArrayListIntoString(deletedPhotoIDs, ","));

//                                 photoFolderList.add(new JSONObject(photoFolderDict));
//                             }
//                         }

//                         if ((taskPhotoUploadStatus != null) && ((taskPhotoUploadStatus.containsKey(taskDetailModel.getTaskID())) || (photoFolderList.size() > 0))) {
//                             HashMap<String, Object> taskDict = new HashMap<>();
//                             taskDict.put("task_id", taskDetailModel.getTaskID());

//                             //mo, this is not reliable: eg, photo1 (suc), photo2 (fail), photo3(suc)
//                             //seems to set default value as true.
//                             taskDict.put("upload_photos_success", taskPhotoUploadStatus.getOrDefault(taskDetailModel.getTaskID(), true));

//                             taskDict.put("modified_time_stamp_photos", dateFormat.format(taskDetailModel.getTaskPhotosModifiedDate()));
//                             taskDict.put("photo_folder", new JSONArray(photoFolderList));

//                             taskList.add(new JSONObject(taskDict));
//                         }
//                     }

//                     requestBodyMap.put("tasks", new JSONArray(taskList));

//                     CommonFunction.printLongString("Uploaded photos count: Network Request: DEL " + new JSONObject(requestBodyMap).toString());

//                     HeaderJsonObjectRequest requestObject = new HeaderJsonObjectRequest(Request.Method.POST, requestURL, new JSONObject(requestBodyMap),
//                             new Response.Listener<JSONObject>() {
//                                 @Override
//                                 public void onResponse(JSONObject responseObject) {
//                                     //mo, 7/11/18, when MD5 check failed, you come here as success with error message
// //                                    CommonFunction.print("Success, network response: " + responseObject.toString());
//                                     CommonFunction.printLongString("Uploaded photos count: Network Request: DEL R1" + responseObject);

//                                     //mo, sync_pic_info ran successfully >> move to task upload.
//                                     if (CommonFunction.isActiveUserExist(mContext)) {
//                                         JSONObject dataObject = responseObject.optJSONObject("data");
//                                         if (dataObject != null) {
//                                             //delete-marked photo models will be physically deleted now

//                                           //  CommonFunction.printLongString("Uploaded photos count: Network Request: DEL R2" + dataObject);

//                                             DatabaseManager.getInstance(mContext).removeDeletedPhotosFromRealm(mRealm, true);
//                                             //checkBeforeProceedForTaskUpload();
//                                         }

//                                         //mo, 7/11/18, Regardless of the response object data is null or not, you still need to move next sync process.
//                                         //Otherwise, "Synchronising..." message hangs out forever. >> Take below function outside of if condition.
//                                       checkBeforeProceedForTaskUpload();
//                                     }
//                                 }
//                             },
//                             new Response.ErrorListener() {
//                                 @Override
//                                 public void onErrorResponse(VolleyError error) {
//                                     CommonFunction.print("Error, error response: " + error.toString());

//                                     checkBeforeProceedForTaskUpload();
//                                 }
//                             });

//                     requestObject.setTag(API_DELETE_TASK_PHOTO_REQUEST_TAG);
//                     addToRequestQueue(requestObject);

//                     return null;
//                 }

//                 @Override
//                 protected void onPostExecute(final Void result) {
//                 }

//             }.execute();
//         } else {
//             checkBeforeProceedForTaskUpload();
//         }
//     }
